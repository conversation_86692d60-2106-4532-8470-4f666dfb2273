use pyo3::prelude::*;
use pyo3::types::{
    PyAny, PyDateAccess, PyDateTime, PyDict, PyList, PyString, PyTimeAccess, PyTuple,
};
use std::collections::HashSet;

/// 格式化 datetime 或其他值为字符串
fn format_datetime<'py>(py: Python<'py>, value: &Bound<'py, PyAny>) -> PyResult<Bound<'py, PyAny>> {
    if let Ok(dt) = value.downcast::<PyDateTime>() {
        let formatted = format!(
            "{:04}-{:02}-{:02} {:02}:{:02}:{:02}",
            dt.get_year(),
            dt.get_month(),
            dt.get_day(),
            dt.get_hour(),
            dt.get_minute(),
            dt.get_second()
        );
        return Ok(PyString::new(py, &formatted).into_any());
    }
    Ok(value.clone())
}

/// classify_by_optimize(data, by_key)
#[pyfunction]
fn classify_by_optimize<'py>(
    py: Python<'py>,
    data: &Bound<'py, PyList>,
    by_key: &str,
) -> PyResult<Bound<'py, PyDict>> {
    let result = PyDict::new(py);

    for item in data.iter() {
        let item_dict: Bound<'py, PyDict> = item.downcast();
        let by_item_val = item_dict
            .get_item(by_key)
            .unwrap_or_else(|| py.None().into_bound(py));
        let by_item_str = format_datetime(py, &by_item_val)?;
        let by_item_string: String = by_item_str.extract()?;

        // 修改原始 dict：_d[by_key] = by_item
        item_dict.set_item(by_key, &by_item_string)?;

        if let Some(existing) = result.get_item(&by_item_string) {
            let existing_list: Bound<'py, PyList> = existing.downcast()?;
            let mut new_list: Vec<_> = existing_list.iter().collect();
            new_list.push(item.clone());
            result.set_item(&by_item_string, PyList::new(py, &new_list))?;
        } else {
            result.set_item(&by_item_string, PyList::new(py, &[item.clone()]))?;
        }
    }

    Ok(result)
}

/// classify_by2_optimize(data, by_key, key_key, value_key, return_columns)
#[pyfunction]
fn classify_by2_optimize<'py>(
    py: Python<'py>,
    data: &Bound<'py, PyList>,
    by_key: &str,
    key_key: &str,
    value_key: &str,
    return_columns: bool,
) -> PyResult<Bound<'py, PyAny>> {
    let merged_data = PyDict::new(py);
    let mut columns_set = HashSet::new();

    for item in data.iter() {
        let entry: Bound<'py, PyDict> = item.downcast()?;

        let by_item = format_datetime(
            py,
            &entry
                .get_item(by_key)
                .unwrap_or_else(|| py.None().into_bound(py)),
        )?;
        let key_key_item = format_datetime(
            py,
            &entry
                .get_item(key_key)
                .unwrap_or_else(|| py.None().into_bound(py)),
        )?;
        let value_key_item = format_datetime(
            py,
            &entry
                .get_item(value_key)
                .unwrap_or_else(|| py.None().into_bound(py)),
        )?;

        // merged_data[by_item] 获取或创建子dict
        let sub_dict = if let Some(existing) = merged_data.get_item(&by_item) {
            existing.downcast::<PyDict>()?
        } else {
            let new_dict = PyDict::new(py);
            merged_data.set_item(&by_item, &new_dict)?;
            new_dict
        };

        sub_dict.set_item(by_key, &by_item)?;
        sub_dict.set_item(&key_key_item, &value_key_item)?;

        columns_set.insert(key_key_item);
    }

    if return_columns {
        // 转换为 list 并排序
        let mut columns: Vec<_> = columns_set.into_iter().collect();
        columns.sort_by(|a, b| {
            a.str()
                .unwrap()
                .to_str()
                .unwrap()
                .cmp(b.str().unwrap().to_str().unwrap())
        });

        let result_tuple = PyTuple::new(
            py,
            &[merged_data.into_any(), PyList::new(py, columns).into_any()],
        );
        Ok(result_tuple.into_any())
    } else {
        Ok(merged_data.into_any())
    }
}
