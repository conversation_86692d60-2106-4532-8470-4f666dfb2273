use pyo3::prelude::*;
use pyo3::types::{PyAny, PyDateAccess, PyDateTime, PyDict, PyList, PyTimeAccess, PyTuple};
use std::collections::{HashMap, HashSet};

/// 将 Python 值转换为 Rust 字符串
fn py_value_to_string(_py: Python, value: &Bound<PyAny>) -> PyResult<String> {
    if let Ok(dt) = value.downcast::<PyDateTime>() {
        let formatted = format!(
            "{:04}-{:02}-{:02} {:02}:{:02}:{:02}",
            dt.get_year(),
            dt.get_month(),
            dt.get_day(),
            dt.get_hour(),
            dt.get_minute(),
            dt.get_second()
        );
        Ok(formatted)
    } else {
        value.str()?.extract()
    }
}

/// 将 Python 字典列表转换为 Rust 数据结构
fn py_list_to_rust_data(
    py: Python,
    data: &Bound<PyList>,
) -> PyResult<Vec<HashMap<String, String>>> {
    let mut result = Vec::new();
    for item in data.iter() {
        let item_dict = item.downcast::<PyDict>()?;
        let mut rust_dict = HashMap::new();

        for (key, value) in item_dict.iter() {
            let key_str: String = key.str()?.extract()?;
            let value_str = py_value_to_string(py, &value)?;
            rust_dict.insert(key_str, value_str);
        }
        result.push(rust_dict);
    }
    Ok(result)
}

/// 纯 Rust 实现的分类逻辑
fn classify_by_rust(
    data: Vec<HashMap<String, String>>,
    by_key: &str,
) -> HashMap<String, Vec<HashMap<String, String>>> {
    let mut result: HashMap<String, Vec<HashMap<String, String>>> = HashMap::new();

    for mut item in data.into_iter() {
        let by_value = item.get(by_key).cloned().unwrap_or_default();

        // 更新原始数据中的 by_key 值
        item.insert(by_key.to_string(), by_value.clone());

        result.entry(by_value).or_insert_with(Vec::new).push(item);
    }

    result
}

/// classify_by_optimize(data, by_key)
#[pyfunction]
#[pyo3(name = "classify_by_optimize_r")]
pub fn classify_by_optimize<'py>(
    py: Python<'py>,
    data: &Bound<'py, PyList>,
    by_key: &str,
) -> PyResult<Bound<'py, PyDict>> {
    // 1. 转换输入数据为 Rust 数据结构
    let rust_data = py_list_to_rust_data(py, data)?;

    // 2. 使用纯 Rust 逻辑处理
    let classified_data = classify_by_rust(rust_data, by_key);

    // 3. 转换结果回 Python 数据结构
    let result = PyDict::new(py);
    for (key, items) in classified_data {
        let py_items = PyList::empty(py);
        for item in items {
            let py_dict = PyDict::new(py);
            for (k, v) in item {
                py_dict.set_item(k, v)?;
            }
            py_items.append(py_dict)?;
        }
        result.set_item(key, py_items)?;
    }

    Ok(result)
}

/// 纯 Rust 实现的第二种分类逻辑
fn classify_by2_rust(
    data: Vec<HashMap<String, String>>,
    by_key: &str,
    key_key: &str,
    value_key: &str,
) -> (HashMap<String, HashMap<String, String>>, HashSet<String>) {
    let mut merged_data: HashMap<String, HashMap<String, String>> = HashMap::new();
    let mut columns_set = HashSet::new();

    for item in data {
        let by_value = item.get(by_key).cloned().unwrap_or_default();
        let key_value = item.get(key_key).cloned().unwrap_or_default();
        let value_value = item.get(value_key).cloned().unwrap_or_default();

        // 获取或创建子字典
        let sub_dict = merged_data
            .entry(by_value.clone())
            .or_insert_with(HashMap::new);

        // 设置值
        sub_dict.insert(by_key.to_string(), by_value);
        sub_dict.insert(key_value.clone(), value_value);

        // 收集列名
        columns_set.insert(key_value);
    }

    (merged_data, columns_set)
}

/// classify_by2_optimize(data, by_key, key_key, value_key, return_columns)
#[pyfunction]
#[pyo3(name = "classify_by2_optimize_r")]
pub fn classify_by2_optimize<'py>(
    py: Python<'py>,
    data: &Bound<'py, PyList>,
    by_key: &str,
    key_key: &str,
    value_key: &str,
    return_columns: bool,
) -> PyResult<Bound<'py, PyAny>> {
    // 1. 转换输入数据为 Rust 数据结构
    let rust_data = py_list_to_rust_data(py, data)?;

    // 2. 使用纯 Rust 逻辑处理
    let (merged_data, columns_set) = classify_by2_rust(rust_data, by_key, key_key, value_key);

    // 3. 转换结果回 Python 数据结构
    let py_merged_data = PyDict::new(py);
    for (key, sub_dict) in merged_data {
        let py_sub_dict = PyDict::new(py);
        for (k, v) in sub_dict {
            py_sub_dict.set_item(k, v)?;
        }
        py_merged_data.set_item(key, py_sub_dict)?;
    }

    if return_columns {
        // 转换为 list 并排序
        let mut columns: Vec<_> = columns_set.into_iter().collect();
        columns.sort();

        let columns_list = PyList::new(py, &columns)?;
        let result_tuple = PyTuple::new(py, &[py_merged_data.into_any(), columns_list.into_any()])?;
        Ok(result_tuple.into_any())
    } else {
        Ok(py_merged_data.into_any())
    }
}
